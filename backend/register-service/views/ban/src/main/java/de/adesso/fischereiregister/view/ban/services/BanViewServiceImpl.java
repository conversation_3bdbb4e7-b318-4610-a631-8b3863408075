package de.adesso.fischereiregister.view.ban.services;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.view.ban.persistence.BanView;
import de.adesso.fischereiregister.view.ban.persistence.BanViewRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Service
@AllArgsConstructor
class BanViewServiceImpl implements BanViewService {

    private final BanViewRepository repository;

    @Override
    public void deleteAll() {
        repository.deleteAll();
    }

    @Override
    public void create(UUID registerEntryId, Ban ban, String federalState) {
        final BanView banView = new BanView();
        banView.setRegisterEntryId(registerEntryId);
        banView.setBanId(ban.getBanId());
        banView.setFileNumber(ban.getFileNumber());
        banView.setReportedBy(ban.getReportedBy());
        banView.setAt(ban.getAt());
        banView.setFrom(ban.getFrom());
        banView.setTo(ban.getTo());
        banView.setFederalState(federalState);
        repository.save(banView);
    }

    @Override
    public void updateFederalState(UUID registerEntryId, String newFederalState) {

        List<BanView> banViews = repository.findByRegisterEntryId(registerEntryId);
        if (banViews.isEmpty()) {
            throw new IllegalStateException("No ban found for register entry id: " + registerEntryId);
        }

        // set the new federal state for the not deleted ban or throw an exception if no ban is found
        banViews.stream().filter(banView -> !banView.isDeleted())
                .forEach(banView -> {
                    banView.setFederalState(newFederalState);
                    repository.save(banView);
                });
    }

    @Override
    public void markBanAsDeleted(UUID registerEntryId) {
        List<BanView> banViews = repository.findByRegisterEntryId(registerEntryId);

        // set deleted flag to true for the one ban that is not deleted yet for the given register entry
        banViews.stream().filter(banView -> !banView.isDeleted())
                .forEach(banView -> {
                    banView.setDeleted(true);
                    repository.save(banView);
                });
    }

    @Override
    public List<UUID> findRegisterEntryIdsWithExpiredBans() {
        return repository.findRegisterEntryIdsWithBansExpiringBefore(LocalDate.now());
    }

    @Override
    public List<Integer> getAvailableYears() {
        return repository.findDistinctIssuedYears();
    }

    @Override
    public Integer getIssuedAmountByFederalStateAndYear(String federalState, Integer year) {
        return repository.countIssuedByFederalStateAndYear(federalState, year);
    }

    @Override
    public Integer getIssuedAmountByYear(Integer year) {
        return repository.countIssuedByYear(year);
    }

    @Override
    public Integer getActiveBansAmountByFederalState(String federalState) {
        return repository.countActiveBansByFederalState(federalState, LocalDate.now());
    }

    @Override
    public Integer getActiveBansAmount() {
        return repository.countActiveBans(LocalDate.now());
    }

}

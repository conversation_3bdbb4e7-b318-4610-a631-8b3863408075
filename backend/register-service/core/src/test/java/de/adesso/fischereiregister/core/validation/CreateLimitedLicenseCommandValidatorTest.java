package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.CreateLimitedLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.LicenseTypeNotSupportedException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.core.validation.helper.LicenseValidatorHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class CreateLimitedLicenseCommandValidatorTest {
    private CreateLimitedLicenseCommandValidator commandValidator;

    private final CountryService countryService = new InterceptableCountryService();

    private final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();

    private final LicenseValidatorHelper licenseValidatorHelper = mock(LicenseValidatorHelper.class);

    @BeforeEach
    void setUp() {
        commandValidator = new CreateLimitedLicenseCommandValidator(countryService, tenantRulesValidationPort, licenseValidatorHelper);
    }


    @Test
    @DisplayName("validateOrThrow should throw an exception when a fee is submitted and a limited license application exists")
    public void testValidateOrThrow_feeSubmittedWithExistingApplication() throws Exception {
        // GIVEN
        final UUID registerEntryId = UUID.randomUUID();

        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setRegisterId(registerEntryId);
        registerEntry.setPerson(DomainTestData.createPersonWithAddress());
        registerEntry.setLimitedLicenseApplication(DomainTestData.createLimitedLicenseApplication());
        registerEntry.setJurisdiction(DomainTestData.createJurisdiction());

        final CreateLimitedLicenseCommand command = new CreateLimitedLicenseCommand(
                registerEntryId,
                "salt",
                DomainTestData.createLimitedLicenseConsentInfo(),
                DomainTestData.createPersonWithAddress(),
                DomainTestData.createAnalogFeesWithOneFee(),
                List.of(),
                DomainTestData.createValidityPeriod(LocalDate.now(), LocalDate.now().plusYears(1)),
                DomainTestData.createLimitedLicenseApproval(),
                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
        );

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(true);

        // WHEN
        // THEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(command, registerEntry));
        assertEquals(1, exception.getValidationResult().getErrorNotes().size());
    }

    @Test
    @DisplayName("validateOrThrow should throw an exception when no fee is submitted and no limited license application exists")
    public void testValidateOrThrow_noFeeSubmittedWithNoExistingApplication() throws Exception {
        // GIVEN
        final UUID registerEntryId = UUID.randomUUID();

        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setRegisterId(registerEntryId);
        registerEntry.setPerson(DomainTestData.createPersonWithAddress());

        final CreateLimitedLicenseCommand command = new CreateLimitedLicenseCommand(
                registerEntryId,
                "salt",
                DomainTestData.createLimitedLicenseConsentInfo(),
                DomainTestData.createPersonWithAddress(),
                List.of(),
                List.of(),
                DomainTestData.createValidityPeriod(LocalDate.now(), LocalDate.now().plusYears(1)),
                DomainTestData.createLimitedLicenseApproval(),
                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
        );

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(true);

        // WHEN
        // THEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(command, registerEntry));
        assertEquals(1, exception.getValidationResult().getErrorNotes().size());
    }

    @Test
    @DisplayName("validateOrThrow should not throw an exception for a valid command")
    public void testValidateOrThrow_validCommand() throws RulesProcessingException {
        // GIVEN
        final UUID registerEntryId = UUID.randomUUID();

        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setRegisterId(registerEntryId);
        registerEntry.setPerson(DomainTestData.createPersonWithAddress());
        registerEntry.setLimitedLicenseApplication(DomainTestData.createLimitedLicenseApplication());
        registerEntry.setJurisdiction(DomainTestData.createJurisdiction());

        final CreateLimitedLicenseCommand command = new CreateLimitedLicenseCommand(
                registerEntryId,
                "salt",
                DomainTestData.createLimitedLicenseConsentInfo(),
                DomainTestData.createPersonWithAddress(),
                List.of(),
                List.of(),
                DomainTestData.createValidityPeriod(LocalDate.now(), LocalDate.now().plusYears(1)),
                DomainTestData.createLimitedLicenseApproval(),
                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
        );

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(true);

        // WHEN
        // THEN
        commandValidator.validateOrThrow(command, registerEntry);
    }

    @Test
    @DisplayName("validateOrThrow should throw an exception when tenant does not support limited licenses")
    public void testValidateOrThrow_tenantDoesNotSupportLimitedLicenses() throws RulesProcessingException {
        // GIVEN
        final UUID registerEntryId = UUID.randomUUID();

        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setRegisterId(registerEntryId);
        registerEntry.setPerson(DomainTestData.createPersonWithAddress());
        registerEntry.setJurisdiction(DomainTestData.createJurisdiction());

        final CreateLimitedLicenseCommand command = new CreateLimitedLicenseCommand(
                registerEntryId,
                "salt",
                DomainTestData.createLimitedLicenseConsentInfo(),
                DomainTestData.createPersonWithAddress(),
                List.of(),
                List.of(),
                DomainTestData.createValidityPeriod(LocalDate.now(), LocalDate.now().plusYears(1)),
                DomainTestData.createLimitedLicenseApproval(),
                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
        );

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(false);

        // WHEN
        // THEN
        assertThrows(LicenseTypeNotSupportedException.class, () -> commandValidator.validateOrThrow(command, registerEntry));
    }

    @Test
    @DisplayName("validateOrThrow should call tenant validation for limited license compliance")
    void validateOrThrow_shouldCallValidateTenantRules() throws Exception {
        // Arrange
        TenantRulesValidationPort tenantRulesValidationPort = mock(TenantRulesValidationPort.class);
        LicenseValidatorHelper licenseValidatorHelper = mock(LicenseValidatorHelper.class);

        // Always return true for license available
        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(true);

        // Use a spy to verify protected method call
        CreateLimitedLicenseCommandValidator validator = Mockito.spy(
                new CreateLimitedLicenseCommandValidator(countryService, tenantRulesValidationPort, licenseValidatorHelper)
        );

        // Provide valid command and registerEntry
        RegisterEntry registerEntry = mock(RegisterEntry.class);

        final CreateLimitedLicenseCommand command = new CreateLimitedLicenseCommand(
                UUID.randomUUID(),
                "salt",
                DomainTestData.createLimitedLicenseConsentInfo(),
                DomainTestData.createPersonWithAddress(),
                List.of(DomainTestData.createAnalogFee()),
                List.of(),
                DomainTestData.createValidityPeriod(LocalDate.now(), LocalDate.now().plusYears(1)),
                DomainTestData.createLimitedLicenseApproval(),
                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
        );

        validator.validateOrThrow(command, registerEntry);

        // Assert
        verify(validator, times(1)).validateTenantRules(
                any(), any(), any(), any(), any()
        );
    }
}

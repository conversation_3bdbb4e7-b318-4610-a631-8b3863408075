package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.CreateLimitedLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.LicenseTypeNotSupportedException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.LimitedLicenseApplicationStatus;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.utils.LicenseUtils;
import de.adesso.fischereiregister.core.validation.helper.LicenseValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.LimitedLicenseApprovalValidatorHelper;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CreateLimitedLicenseCommandValidator extends AbstractValidator implements CommandValidator<CreateLimitedLicenseCommand> {

    private final LicenseValidatorHelper licenseValidatorHelper;

    public CreateLimitedLicenseCommandValidator(
            CountryService countryService,
            TenantRulesValidationPort tenantRulesValidationPort,
            LicenseValidatorHelper licenseValidatorHelper) {
        super(countryService, tenantRulesValidationPort);

        this.licenseValidatorHelper = licenseValidatorHelper;
    }

    @Override
    public void validateOrThrow(final CreateLimitedLicenseCommand command, final RegisterEntry registerEntry) throws AggregateValidationException, RulesProcessingException {
        validateUserDetails(command.userDetails());
        validateFieldRequired(command.salt(), SALT, validationResult);

        if (validationResult.hasErrors()) {
            // If the salt value is invalid, it is due to a system misconfiguration, so we return an exception accordingly.
            throw new SystemConfigValidationException(validationResult);
        }

        // Ensure that tenant supports limited type licenses
        FederalState federalState = FederalState.valueOf(command.userDetails().getFederalState());
        if (!licenseValidatorHelper.isLicenseAvailable(federalState, LicenseType.LIMITED)) {
            throw new LicenseTypeNotSupportedException(LicenseType.LIMITED, federalState);
        }

        if (LicenseUtils.hasRegularLicense(registerEntry)) {
            validationResult.addErrorNote("Regular License allreday exists for this Register Entry so Limited License cannot be created");
        }

        validatePerson(command.person(), true);
        final Address address = Optional.ofNullable(command.person())
                .map(person -> person.getAddress() != null ? person.getAddress() : person.getOfficeAddress())
                .orElse(null);
        validateAddress(address);

        validateTaxes(command.taxes());

        if (isLimitedLicenseAppliedViaOS(registerEntry)) {
            if (command.fees() != null && !command.fees().isEmpty()) {
                validationResult.addErrorNote("Fees must not be provided when a pending license application exists, since it was already payed.");
            }
        } else {
            validateFieldRequired(command.fees(), FEES, validationResult);
            validateFees(command.fees());
        }

        validateConsentInfo(command.consentInfo());

        validateTenantRules(FederalState.valueOf(command.userDetails().getFederalState()), command.person(), command.taxes(), command.fees(), LicenseType.LIMITED);

        validateValidityPeriod(command.validityPeriod(), false, true);

        validateLicenseApproval(command.limitedLicenseApproval(), command.validityPeriod());

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }

    /**
     * Checks whether the limited license has been applied via a Online service (OS) or not.
     * <p>
     * When a limited license is applied via an OS, the fee has already been paid and must not be paid again.
     */
    private boolean isLimitedLicenseAppliedViaOS(RegisterEntry registerEntry) {
        LimitedLicenseApplication application = registerEntry.getLimitedLicenseApplication();

        if (application == null) {
            return false;
        }

        return application.getStatus() == LimitedLicenseApplicationStatus.PENDING;
    }

    private void validateLicenseApproval(LimitedLicenseApproval limitedLicenseApproval, ValidityPeriod validityPeriod) {
        LimitedLicenseApprovalValidatorHelper.validateLimitedLicenseApproval(limitedLicenseApproval, validityPeriod, validationResult);
    }

}

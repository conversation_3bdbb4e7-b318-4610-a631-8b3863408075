package de.adesso.fischereiregister.registerservice.fishing_license_export;

import de.adesso.fischereiregister.core.exceptions.JurisdictionMismatchException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import org.springframework.security.access.AccessDeniedException;

import java.util.List;
import java.util.UUID;


public interface FishingLicenseExportService {

    /**
     * Exports a fishing license as a PDF document.
     *
     * @param registerId The ID of the register entry.
     * @param documentId The ID of the identification document.
     * @return A {@link RenderedContent} object containing the PDF content and metadata.
     */
    RenderedContent exportFishingLicense(UUID registerId, String documentId);

    /**
     * Exports a fishing license as a PDF document.
     *
     * @param registerId The ID of the register entry.
     * @param salt       The salt used to generate the QR code.
     * @param person     The person associated with the fishing license.
     * @param document   The identification document containing the fishing license.
     * @return A {@link RenderedContent} object containing the PDF content and metadata.
     */
    RenderedContent exportFishingLicense(UUID registerId, String salt, Person person, IdentificationDocument document);

    /**
     * Exports a fishing tax document as a PDF.
     *
     * @param registerId The ID of the register entry.
     * @param documentId The ID of the IdentificationDocument containing the tax information.
     * @return A {@link RenderedContent} object containing the PDF content and metadata.
     */
    RenderedContent exportFishingTaxDocument(UUID registerId, String documentId);

    /**
     * Exports a fishing tax document as a PDF.
     *
     * @param registerEntryId The ID of the register entry.
     * @param salt            The salt used to generate the QR code.
     * @param person          The person associated with the fishing tax.
     * @param document        The identification document containing the tax information.
     * @return A {@link RenderedContent} object containing the PDF content and metadata.
     */
    RenderedContent exportFishingTaxDocument(UUID registerEntryId, String salt, Person person, IdentificationDocument document);

    /**
     * Exports a fishing certificate as a PDF document.
     *
     * @param fishingCertificateId The ID of the fishing certificate.
     * @return A {@link RenderedContent} object containing the PDF content and metadata.
     */
    RenderedContent exportFishingCertificate(String fishingCertificateId) throws RulesProcessingException;

    /**
     * Exports a limited license approval as a PDF document.
     *
     * @param registerEntryId The ID of the register entry.
     * @param documentId      The ID of the identification document.
     * @param userDetails     The user details of the user requesting the export.
     * @return A {@link RenderedContent} object containing the PDF content and metadata.
     * @throws JurisdictionMismatchException if the jurisdiction of the user does not match the jurisdiction of the limited license whose approval is to be exported
     * @throws AccessDeniedException         if the user does not have the necessary role to access limited license approvals
     */
    RenderedContent exportLimitedLicenseApproval(UUID registerEntryId, String documentId, UserDetails userDetails) throws RulesProcessingException, JurisdictionMismatchException, AccessDeniedException;

    /**
     * Exports a limited license approval preview as a PDF document.
     *
     * @param person                 The person associated with the limited license approval.
     * @param validityPeriod         The validity period of the limited license.
     * @param federalState           The federal state where the limited license is issued.
     * @param taxes                  The taxes associated with the limited license.
     * @param limitedLicenseApproval The limited license approval.
     * @return A {@link RenderedContent} object containing the PDF content and metadata.
     */
    RenderedContent exportLimitedLicenseApprovalPreview(Person person, ValidityPeriod validityPeriod, String federalState, List<Tax> taxes, LimitedLicenseApproval limitedLicenseApproval) throws RulesProcessingException;
}

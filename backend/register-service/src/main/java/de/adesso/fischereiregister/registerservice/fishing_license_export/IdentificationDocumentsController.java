package de.adesso.fischereiregister.registerservice.fishing_license_export;

import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.domain.mapper.IdentificationDocumentsMailTemplateTypeMapper;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.mail.enums.MailTemplate;
import de.adesso.fischereiregister.registerservice.mail.identification_documents_mail.IdentificationDocumentsMailService;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryView;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewService;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.JRRuntimeException;
import org.apache.commons.lang3.NotImplementedException;
import org.openapitools.model.ExportType;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;


@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class IdentificationDocumentsController implements api.IdentificationDocumentsApi {

    private final FishingLicenseExportService fishingLicenseExportService;
    private final IdentificationDocumentsMailService identificationDocumentsMailService;
    private final RegisterEntryViewService registerEntryViewService;
    private final UserDetailsService userDetailsService;

    @Override
    public ResponseEntity<?> identificationDocumentsControllerGetAllByIds(String registerEntryId, List<String> documentIds) {
        log.debug("Request received to fetch identification documents for registerEntryId: {} with documentIds: {}", registerEntryId, documentIds);

        try {
            final UserDetails userDetails = userDetailsService.getUserDetails()
                    .orElseThrow(() -> {
                        log.error("Failed to retrieve user details: User details are missing or corrupted.");
                        return new IllegalStateException("The User Auth Information is missing or corrupted");
                    });

            final RegisterEntryView registerEntryView = this.registerEntryViewService.findRegisterEntryByIdFilterIdentificationDocumentsByUserDetails(UUID.fromString(registerEntryId), userDetails);
            log.debug("Successfully fetched RegisterEntryView for registerEntryId: {}", registerEntryId);

            final List<IdentificationDocument> filteredDocuments = registerEntryView.getData()
                    .getIdentificationDocuments()
                    .stream()
                    .filter(doc -> documentIds.contains(doc.getDocumentId()))
                    .toList();

            final Set<String> foundDocumentIds = filteredDocuments.stream()
                    .map(IdentificationDocument::getDocumentId)
                    .collect(Collectors.toSet());

            final List<String> missingDocumentIds = documentIds.stream()
                    .filter(id -> !foundDocumentIds.contains(id))
                    .toList();

            if (!missingDocumentIds.isEmpty()) {
                log.warn("The following document IDs were not found: {}", missingDocumentIds);
                throw new EntityNotFoundException("Document IDs not found: " + missingDocumentIds);
            }

            log.debug("Successfully found {} identification documents for registerEntryId: {}", filteredDocuments.size(), registerEntryId);

            return ResponseEntity.ok(filteredDocuments);

        } catch (EntityNotFoundException ex) {
            log.warn("Identification Documents for Register Entry with id {} not found, error response: {}", registerEntryId, ex.getMessage());
            return ResponseEntity.notFound().build();
        }

    }


    @SneakyThrows
    @Override
    public ResponseEntity<?> identificationDocumentsControllerSendMail(String registerEntryId, List<String> documentIds, String emailAddress, org.openapitools.model.IdentificationDocumentsMailTemplateType templateTypeApi) {

        final UserDetails userDetails = userDetailsService.getUserDetails()
                .orElseThrow(() -> {
                    log.error("Failed to retrieve user details: User details are missing or corrupted.");
                    return new IllegalStateException("The User Auth Information is missing or corrupted");
                });

        final MailTemplate mailTemplate = IdentificationDocumentsMailTemplateTypeMapper.INSTANCE.toIdentificationDocumentMailTemplate(templateTypeApi);

        identificationDocumentsMailService.createAndSendMail(UUID.fromString(registerEntryId), documentIds, emailAddress, mailTemplate, userDetails);

        return ResponseEntity.ok().build();
    }


    @Override
    @SneakyThrows
    public ResponseEntity<Resource> identificationDocumentsControllerExport(String registerEntryId,
                                                                            String documentId,
                                                                            ExportType type) {
        final UUID registerIdAsUUID = UUID.fromString(registerEntryId);
        final RenderedContent renderedContent;
        try {
            switch (type) {
                case FISHING_LICENSE ->
                        renderedContent = fishingLicenseExportService.exportFishingLicense(registerIdAsUUID, documentId);
                case FISHING_TAXES ->
                        renderedContent = fishingLicenseExportService.exportFishingTaxDocument(registerIdAsUUID, documentId);
                case LIMITED_LICENSE_APPROVAL -> {
                    final UserDetails userDetails = userDetailsService.getUserDetails()
                            .orElseThrow(() -> {
                                log.error("Failed to retrieve user details: User details are missing or corrupted.");
                                return new IllegalStateException("The User Auth Information is missing or corrupted");
                            });
                    renderedContent = fishingLicenseExportService.exportLimitedLicenseApproval(registerIdAsUUID, documentId, userDetails);
                }
                default -> throw new NotImplementedException("Unsupported export type: " + type.name());
            }
        } catch (JRRuntimeException e) {
            log.error("JRException while exporting PDF", e);
            return ResponseEntity.unprocessableEntity().build();
        }

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + renderedContent.getFullFilename() + "\"")
                .contentType(renderedContent.type().getMediaType())
                .body(new ByteArrayResource(renderedContent.content()));

    }
}

spring:
  cloud:
    aws:
      s3:
        endpoint: http://localhost:9000
        region: eu-central-1
        bucket: digifischdok
      credentials:
        access-key: minioadmin
        secret-key: minioadmin
  datasource:
    jdbcUrl: **************************************************************************************************
    username: digifischdok
    password: digifischdok
  security:
    oauth2:
      client:
        registration:
          keycloak:
            client-id: local-api-client
            client-secret: ${KEYCLOAK_CLIENT_SECRET:'<Please set key cloak secret>'}
            authorization-grant-type: authorization_code
            scope: openid
        provider:
          keycloak:
            issuer-uri: https://digifischdok-iam.dsecurecloud.de:8443/realms/digifisch-dev
            user-name-attribute: preferred_username
      resourceserver:
        jwt:
          issuer-uri: https://digifischdok-iam.dsecurecloud.de:8443/realms/digifisch-dev
  mail:
    host: localhost
    port: 1025
    username: none
    password: none
    debug: true
    from:
      address: <EMAIL>

message-service:
  token-uri: https://idp.serviceportal-stage.gemeinsamonline.de/webidp2/connect/token
  client-id: urn:digifischdok:stage
  client-secret: ${OS_INDBOX_CLIENT_SECRET:'<This secret has to be set in order to test OD inbox integration>'}
  authorization-grant-type: client_credentials
  scope: access_urn:dataport:od:digifischdok:stage:go:DigiFischDok,default
  resource: urn:dataport:osi:postfach:rz2:stage:go
  base-urn: https://api-gateway-stage.dataport.de:443

management:
  endpoints:
    web:
      exposure:
        include: health,info,logfile,loggers,threaddump
  info:
    java:
      enabled: true
    env:
      enabled: true
  tracing:
    sampling:
      probability: 1.0
  metrics:
    distribution:
      percentiles-histogram:
        http:
          server:
            requests: true

logging:
  level:
    org:
      springframework:
        security: DEBUG
    web: DEBUG

test-data-import:
  upload-endpoint-enabled: true
  local-file-at-startup-enabled: true
  path: src/main/resources/config/testdata/test-data.csv

migrations:
  # Will force a replay of ALL events on startup forcing also all upcasters to be applied.
  # This might cause heavy initial lag, since replaying events is very expensive.
  replay_events_on_startup: true
  initial_delay_in_minutes: 0 # Initial delay after app startup to wait, until migration is attempted
